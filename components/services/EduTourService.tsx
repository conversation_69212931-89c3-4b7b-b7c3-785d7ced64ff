'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  GraduationCap, 
  Users, 
  Globe, 
  Target, 
  Heart, 
  Lightbulb,
  ArrowRight,
  CheckCircle,
  BookOpen,
  Award
} from 'lucide-react';

const educationalBenefits = [
  {
    icon: Lightbulb,
    title: 'Broaden students thinking',
    description: 'Expand perspectives beyond traditional classroom boundaries'
  },
  {
    icon: BookOpen,
    title: 'Provides opportunities for students to learn from the experiences',
    description: 'Real-world learning through hands-on experiences'
  },
  {
    icon: Heart,
    title: 'Provide opportunities for meaningful service',
    description: 'Engage in community service and social responsibility'
  },
  {
    icon: Target,
    title: 'Provide opportunities for adventure and challenge',
    description: 'Push boundaries and overcome personal limitations'
  },
  {
    icon: Award,
    title: 'Develop student skills so they can move on with confidence',
    description: 'Build essential life skills for future success'
  },
  {
    icon: Users,
    title: 'Provide opportunities for personal and social development',
    description: 'Foster interpersonal skills and emotional intelligence'
  },
  {
    icon: Globe,
    title: 'Increase students understanding of different perspectives and different cultures',
    description: 'Cultivate global awareness and cultural sensitivity'
  },
  {
    icon: GraduationCap,
    title: 'Promote appreciation of our heritage on a global level',
    description: 'Connect local heritage with global perspectives'
  },
  {
    icon: CheckCircle,
    title: 'Positively influence the next generation by developing their awareness of the wider world',
    description: 'Shape future leaders with global consciousness'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export default function EduTourService() {
  return (
    <div className="py-16 lg:py-24">
      <div className="container-custom">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
              Positive7 EduTour
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Educational Tours that transform learning experiences
          </p>
        </motion.div>

        {/* Main Content Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Educational Tour
              </span>
            </h2>
            
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                It is widely agreed that every student should experience the world beyond the classroom. 
                Whatever the subject, with a Student Positive7 you can be sure that learning outcomes will 
                be met and students will be engaged and inspired like never before.
              </p>
              
              <p>
                With our extremely knowledgeable ground staff we will tailor-make you a tour that will 
                open students' eyes to global diversity, inspire them to consider their own career path 
                and provide invaluable experiences all-round, all while raising the profile of your department.
              </p>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative h-96 lg:h-full rounded-2xl overflow-hidden"
          >
            <Image
              src="/images/fallback-image.jpg"
              alt="Educational Tour Experience"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </motion.div>
        </div>

        {/* Benefits Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-16"
        >
          <motion.h3
            variants={itemVariants}
            className="text-3xl font-bold mb-12 text-center"
          >
            <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
              Our Educational tour is designed to
            </span>
          </motion.h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {educationalBenefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-2 bg-primary-100 rounded-lg">
                      <IconComponent className="h-6 w-6 text-primary-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2 leading-tight">
                        {benefit.title}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Ready to Transform Learning?
              </span>
            </h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Join thousands of students who have experienced education beyond the classroom. 
              Let us create a customized educational tour that meets your learning objectives.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-3 bg-white text-primary-600 font-medium rounded-full hover:bg-gray-100 transition-colors duration-300 hover:scale-105 transform"
              >
                Plan Your EduTour
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link
                href="/trips"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-primary-600 transition-all duration-300 hover:scale-105 transform"
              >
                View Available Trips
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
