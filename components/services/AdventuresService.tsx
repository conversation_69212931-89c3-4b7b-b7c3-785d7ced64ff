'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Mountain, 
  Users, 
  Compass, 
  Shield,
  ArrowRight,
  Target,
  Zap,
  TreePine,
  Map
} from 'lucide-react';

const adventurePrograms = [
  {
    icon: Target,
    title: 'Main & Wider Key Skills',
    description: 'We have programmes to push participants to develop their key skills. Whether the aim is problem solving, working with others or focusing on their own personal development and communication, participants will be faced with challenges that will encourage them to use, develop and review personal skills, often well outside their comfort zones.',
    color: 'from-blue-500 to-indigo-600',
    bgColor: 'bg-blue-50'
  },
  {
    icon: Users,
    title: 'Team Building & Personal Development',
    description: 'A commonly used term, but we realize that the ability to work in a team is fundamental to a young person\'s development and is a skill which potential employers look for. Whether it is linked with key skills, targeting specific issues within a group, developing individuals within a team, or simply bringing a group of people closer together, we can make participant travel trips fit the end goal.',
    color: 'from-green-500 to-emerald-600',
    bgColor: 'bg-green-50'
  },
  {
    icon: Compass,
    title: 'Expeditions',
    description: 'Using the camping and the elements with which we have little control over presents a challenge in itself and participants are certainly put to the test in planning, preparing and carrying out their own camping expedition. Our team of experts will be on hand to guide the participants throughout but we expect our expedition trips to be led by the group, creating a great tool for team building and developing young people as individuals.',
    color: 'from-orange-500 to-amber-600',
    bgColor: 'bg-orange-50'
  },
  {
    icon: Shield,
    title: 'Survival & Camp Craft',
    description: 'As part of a specific multi day programme or a brief session, we can take participants back to basics to not only learn survival skills but also learn independence, adapting to the basic instinct to survive, whilst experiencing and appreciating the importance and value of the tools provided within the surrounding natural environment.',
    color: 'from-red-500 to-rose-600',
    bgColor: 'bg-red-50'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export default function AdventuresService() {
  return (
    <div className="py-16 lg:py-24">
      <div className="container-custom">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
              Positive7 Adventures
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Outdoor learning experiences that challenge, inspire, and transform
          </p>
        </motion.div>

        {/* Main Content Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Positive7 Adventures
              </span>
            </h2>
            
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                Using the Camping as a learning tool has huge academic benefits in creating scenarios 
                that participants may never have been presented with or experienced before. Working with 
                schools and colleges we have developed study travel programmes across a number of areas 
                where we incorporate adventure Camping to achieve specific learning outcomes for young people.
              </p>
            </div>

            {/* Key Features */}
            <div className="grid grid-cols-2 gap-4 mt-8">
              <div className="flex items-center space-x-3 p-4 bg-white/70 rounded-lg border border-white/50">
                <Mountain className="h-6 w-6 text-green-600" />
                <span className="font-medium text-gray-900">Outdoor Learning</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-white/70 rounded-lg border border-white/50">
                <Users className="h-6 w-6 text-blue-600" />
                <span className="font-medium text-gray-900">Team Building</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-white/70 rounded-lg border border-white/50">
                <Zap className="h-6 w-6 text-orange-600" />
                <span className="font-medium text-gray-900">Skill Development</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-white/70 rounded-lg border border-white/50">
                <TreePine className="h-6 w-6 text-emerald-600" />
                <span className="font-medium text-gray-900">Nature Connection</span>
              </div>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative h-96 lg:h-full rounded-2xl overflow-hidden"
          >
            <Image
              src="/images/fallback-image.jpg"
              alt="Adventure Camping Experience"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </motion.div>
        </div>

        {/* Adventure Programs */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-16"
        >
          <motion.h3
            variants={itemVariants}
            className="text-3xl font-bold mb-12 text-center"
          >
            <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
              Our Adventure Programs
            </span>
          </motion.h3>
          
          <div className="space-y-8">
            {adventurePrograms.map((program, index) => {
              const IconComponent = program.icon;
              
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className={`${program.bgColor} rounded-2xl p-8 border border-white/50 hover:shadow-lg transition-all duration-300`}
                >
                  <div className="flex flex-col lg:flex-row lg:items-start space-y-4 lg:space-y-0 lg:space-x-6">
                    <div className={`flex-shrink-0 p-4 bg-gradient-to-r ${program.color} rounded-xl`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-gray-900 mb-4">
                        {program.title}
                      </h4>
                      <p className="text-gray-600 leading-relaxed">
                        {program.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              <span className="bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Ready for an Adventure?
              </span>
            </h3>
            <p className="text-green-100 mb-6 max-w-2xl mx-auto">
              Challenge yourself and your team with our adventure programs. Build skills, 
              create memories, and discover what you're truly capable of in the great outdoors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-3 bg-white text-green-600 font-medium rounded-full hover:bg-gray-100 transition-colors duration-300 hover:scale-105 transform"
              >
                Start Your Adventure
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link
                href="/trips"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-green-600 transition-all duration-300 hover:scale-105 transform"
              >
                View Adventure Trips
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
