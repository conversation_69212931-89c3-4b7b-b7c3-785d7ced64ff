'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Heart, 
  Shield, 
  Users, 
  Star,
  ArrowRight,
  Sparkles,
  Crown,
  Compass,
  Coffee
} from 'lucide-react';

const empowermentFeatures = [
  {
    icon: Crown,
    title: 'Self-Discovery',
    description: 'Find your real self, peace and interests that had never been explored before'
  },
  {
    icon: Shield,
    title: 'Safety First',
    description: 'Accompanied by certified outdoor leaders with carefully crafted itineraries'
  },
  {
    icon: Users,
    title: 'Community Building',
    description: 'Create bonds with other women travelers and overcome fears together'
  },
  {
    icon: Sparkles,
    title: 'Break Barriers',
    description: 'Shatter self-consciousness and break old mindsets that restrict dreams'
  },
  {
    icon: Heart,
    title: 'Me Time',
    description: 'Escape from monotony and stress of work and daily chores at home'
  },
  {
    icon: Star,
    title: 'Age No Bar',
    description: 'Young at heart and inclined to explore - that\'s all you need'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export default function GirlsGoSoloService() {
  return (
    <div className="py-16 lg:py-24">
      <div className="container-custom">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
            Positive7 Girls Go Solo
          </h1>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Empowering women through transformative solo travel experiences
          </p>
        </motion.div>

        {/* Main Content Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Positive7 girls go solo
            </h2>
            
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p className="text-lg font-medium text-pink-600">
                Girls Go Solo trip means fun, energy, rejuvenation and me time. 
                It's something every woman MUST do.
              </p>
              
              <p>
                We support the idea of women being enough for themselves! Being a woman is not easy, 
                it comes with lots of responsibilities and roles. We firmly believe that women should 
                take some time for finding their real self, peace and interest that had never been explored. 
                The major focus of Girls Go Solo is to bring change in the life of women. The idea is to 
                break the old mindset that restricts them to follow their dreams.
              </p>
            </div>

            {/* Key Highlights */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
              <div className="flex items-center space-x-3 p-4 bg-pink-50 rounded-lg border border-pink-100">
                <Heart className="h-6 w-6 text-pink-600" />
                <span className="font-medium text-gray-900">Self-Empowerment</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg border border-purple-100">
                <Shield className="h-6 w-6 text-purple-600" />
                <span className="font-medium text-gray-900">Safe Travel</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-rose-50 rounded-lg border border-rose-100">
                <Users className="h-6 w-6 text-rose-600" />
                <span className="font-medium text-gray-900">Women Community</span>
              </div>
              <div className="flex items-center space-x-3 p-4 bg-orange-50 rounded-lg border border-orange-100">
                <Coffee className="h-6 w-6 text-orange-600" />
                <span className="font-medium text-gray-900">Me Time</span>
              </div>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative h-96 lg:h-full rounded-2xl overflow-hidden"
          >
            <Image
              src="/images/fallback-image.jpg"
              alt="Women Solo Travel Experience"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </motion.div>
        </div>

        {/* Detailed Description */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-2xl p-8 mb-16 border border-white/50"
        >
          <div className="space-y-4 text-gray-600 leading-relaxed">
            <p>
              We offer women, like you, an escape from the monotony and stress of work and daily chores at home. 
              Travel, in itself, helps women unwind, relax and enjoy some 'me time' but making the journey in the 
              company of other women travelers elevates it to something special. It helps create bonds, overcome 
              fears and shatter self-consciousness.
            </p>
            
            <p>
              <strong className="text-pink-600">With Girls Go Solo, safety is our top priority.</strong> You will be 
              accompanied by certified outdoor leaders and our itineraries are carefully made to suit the taste of an 
              adventurous female traveler. Age is truly no bar when you are travelling with us. You just have to be 
              young at heart and inclined to explore the best travel destinations for solo trips. It's our promise to 
              provide all the curious women the best and unique experience of their lives.
            </p>
          </div>
        </motion.div>

        {/* Empowerment Features */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-16"
        >
          <motion.h3 
            variants={itemVariants}
            className="text-3xl font-bold text-gray-900 mb-12 text-center"
          >
            Why Choose Girls Go Solo?
          </motion.h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {empowermentFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-900 mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Embrace Your Independence?
            </h3>
            <p className="text-pink-100 mb-6 max-w-2xl mx-auto">
              Join our community of empowered women travelers. Discover your strength, 
              build lasting friendships, and create memories that will inspire you for life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-3 bg-white text-pink-600 font-medium rounded-full hover:bg-gray-100 transition-colors duration-300 hover:scale-105 transform"
              >
                Join Girls Go Solo
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link
                href="/trips"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-pink-600 transition-all duration-300 hover:scale-105 transform"
              >
                View Solo Trips
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
