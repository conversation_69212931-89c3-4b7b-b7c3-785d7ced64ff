import { Metadata } from 'next';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ServicesOverview from '@/components/services/ServicesOverview';
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import CacheManager from '@/components/cache/CacheManager';

export const metadata: Metadata = {
  title: 'Our Services - Positive7 Educational Tours',
  description: 'Discover our comprehensive range of educational and adventure services including EduTours, Adventures, Girls Go Solo trips, and Holiday packages. Experience learning beyond the classroom with Positive7.',
  keywords: 'educational tours, adventure camps, girls solo travel, student trips, experiential learning, outdoor education, team building, camping, Gujarat tourism',
  openGraph: {
    title: 'Our Services - Positive7 Educational Tours',
    description: 'Comprehensive educational and adventure services for students and travelers. From classroom to adventure - experience learning like never before.',
    type: 'website',
    images: [
      {
        url: '/images/services-hero.jpg',
        width: 1200,
        height: 630,
        alt: 'Positive7 Services - Educational Tours and Adventures',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Services - Positive7 Educational Tours',
    description: 'Comprehensive educational and adventure services for students and travelers.',
    images: ['/images/services-hero.jpg'],
  },
};

export default function ServicesPage() {
  return (
    <PageErrorBoundary context="services-page">
      <CacheManager 
        cacheKey="services-page"
        dependencies={['services']}
        ttl={300000} // 5 minutes
      >
        <Header />
        <main className="flex-1">
          <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
            <Suspense fallback={
              <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
              </div>
            }>
              <ServicesOverview />
            </Suspense>
          </div>
        </main>
        <Footer />
      </CacheManager>
    </PageErrorBoundary>
  );
}
